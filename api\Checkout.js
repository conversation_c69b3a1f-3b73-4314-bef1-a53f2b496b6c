import axiosClient from "../AxiosClient";

// Helper function to delay execution
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to check if error is retryable
const isRetryableError = (error) => {
  if (!error.response) return true; // Network errors are retryable

  const status = error.response.status;
  const errorDetail = error.response.data?.detail || '';

  // Retry on server errors (5xx) and specific database connection issues
  return status >= 500 ||
         errorDetail.includes('QueuePool') ||
         errorDetail.includes('connection timed out') ||
         errorDetail.includes('timeout');
};

export const CheckoutApi = async (data, maxRetries = 3) => {
  console.log("🔄 CheckoutApi called with data:", JSON.stringify(data, null, 2));
  console.log("🌐 Making request to: user/checkout/create");

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Attempt ${attempt}/${maxRetries}`);

      const response = await axiosClient.post(`user/checkout/create`, data);
      console.log("✅ CheckoutApi response:", JSON.stringify(response.data, null, 2));
      return response.data;

    } catch (error) {
      console.log(`❌ CheckoutApi error on attempt ${attempt}:`, error);

      if (error.response) {
        console.log("📊 Error response status:", error.response.status);
        console.log("📋 Error response data:", JSON.stringify(error.response.data, null, 2));
        console.log("📝 Error response headers:", error.response.headers);
      }

      // If this is the last attempt or error is not retryable, throw the error
      if (attempt === maxRetries || !isRetryableError(error)) {
        console.log(`🚫 Not retrying. Attempt ${attempt}/${maxRetries}, Retryable: ${isRetryableError(error)}`);
        throw error;
      }

      // Calculate delay with exponential backoff: 1s, 2s, 4s
      const delayMs = Math.pow(2, attempt - 1) * 1000;
      console.log(`⏳ Retrying in ${delayMs}ms...`);
      await delay(delayMs);
    }
  }
};

export const GetCheckoutApi = async () => {
  const response = await axiosClient.get(`user/checkout/get`);
  return response.data;
};

export const getSuggestions = async (vendor_id, item_id) => {
  console.log("ids in parsms");
  console.log(vendor_id, item_id);
  const response = await axiosClient.get(
    `user/discover/items/suggestions/get?vendor_id=${vendor_id}&item_id=${item_id}`
  );
  console.log("Inside get suggestions API...");
  console.log(response.data);
  return response.data;
};

export const getCoupons = async () => {
  const response = await axiosClient.get(`user/checkout/coupon-code/get`);
  return response.data;
};
